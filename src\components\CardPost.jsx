import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
} from "@material-tailwind/react";
import axios from "axios";
import defaultAvatar from "../assets/avatar.png";
import { useEffect, useState, useContext } from "react";
import { IoSend } from "react-icons/io5";
import { AuthContext } from "../contexts/AuthContext";


export default function CardPost() {
  const { baseUrl } = useContext(AuthContext);

  const [posts, setPosts] = useState([]);

  useEffect(() => {
    
    axios.get(`${baseUrl}/posts`).then((response) => {
      setPosts(response.data.data);
    });
  }, [ ]);

  return (
    <>
      {posts.map((post) => (
        <Card key={post.id} className="max-w-2xl mx-auto my-5 shadow-lg">
          <div className="flex items-center gap-2 p-2">
            <img
              src={post.author?.profile_image || defaultAvatar}
              onError={(e) => {
                e.currentTarget.src = defaultAvatar;
              }}
              alt={post.author?.username || "User"}
              className="h-8 w-8 rounded-full object-cover"
            />
            <Typography variant="small" color="gray" className="font-semibold">
              {post.author.username}
            </Typography>
          </div>

          <CardHeader className="h-72">
            <img
              src={post.image}
              alt="Post cover"
              className="h-full w-full object-cover rounded-lg"
            />
          </CardHeader>

          <CardBody>
            <Typography variant="small" color="gray">
              {post.created_at}
            </Typography>
            <Typography
              variant="h4"
              className="mb-2 text-2xl first-letter:capitalize first-letter:text-red-700 underline font-semibold "
            >
              {post.title}
            </Typography>
            <Typography variant="paragraph" color="blue-gray" className="my-1">
              {post.body}
            </Typography>
            <hr className="border border-gray-400" />
          </CardBody>

          <CardFooter>
            <div className="flex items-center gap-2 flex-wrap ">
              <Typography
                as="div"
                variant="small"
                color="gray"
                className="flex items-center gap-2  cursor-pointer hover:text-gray-900 "
              >
                <IoSend className="text-gray-500 hover:text-gray-900 transition-all duration-300" />
                ({post.comments_count}) Comments
              </Typography>

              <Chip
                variant="ghost"
                size="sm"
                value="Tag 1"
                className="text-gray-300 bg-gray-800 p-1"
              >
                Tag 1
              </Chip>
              <Chip
                variant="ghost"
                size="sm"
                value="Tag 1"
                className="text-gray-300 bg-gray-800 p-1"
              >
                Tag 1
              </Chip>
              <Chip
                variant="ghost"
                size="sm"
                value="Tag 1"
                className="text-gray-300 bg-gray-800 p-1"
              >
                Tag 1
              </Chip>
            </div>
          </CardFooter>
        </Card>
      ))}
    </>
  );
}
