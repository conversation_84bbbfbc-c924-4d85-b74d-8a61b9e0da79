import * as React from "react";
import {
  <PERSON><PERSON>,
  IconButton,
  Ty<PERSON><PERSON>,
  Collapse,
  Navbar,
} from "@material-tailwind/react";
import { Home, Menu, ProfileCircle, Xmark } from "iconoir-react";
import ModalLogin from "./Modals/ModalLogin.jsx";
import ModalRegister from "./Modals/ModalRegister.jsx";

const LINKS = [
  {
    icon: Home,
    title: "Home",
    href: "#",
  },
  {
    icon: ProfileCircle,
    title: "Profile",
    href: "#",
  },
];

function NavList() {
  return (
    <ul className="mt-4 flex flex-col gap-x-3 gap-y-1.5 lg:mt-0 lg:flex-row lg:items-center">
      {LINKS.map(({ icon: Icon, title, href }) => (
        <li key={title}>
          <Typography
            as="a"
            href={href}
            type="small"
            className="flex items-center gap-x-2 p-1 text-white hover:text-white"
          >
            <Icon className="h-4 w-4" />
            {title}
          </Typography>
        </li>
      ))}
    </ul>
  );
}

export default function DarkNavbar() {
  const [openNav, setOpenNav] = React.useState(false);
  const [openModal, setOpenModal] = React.useState(false);
  const [type, setType] = React.useState("login");

  const handleOpenModal = (type) => {
    setType(type);
    setOpenModal(true);
  };
  const handleCloseModal = () => {
    setOpenModal(false);
  };

  React.useEffect(() => {
    window.addEventListener(
      "resize",
      () => window.innerWidth >= 960 && setOpenNav(false)
    );
  }, []);

  return (
    <>
      <Navbar className="mx-auto  max-w-2xl px-4 bg-black dark:bg-surface-dark">
        <div className="flex items-center text-white justify-between">
          <Typography
            as="a"
            href="#"
            type="h3"
            className="ml-2 mr-2 block py-1 font-bold"
          >
            V <span className="text-red-500">S</span>
          </Typography>

          <hr className="ml-1 mr-1.5 hidden h-5 w-px border-l border-t-0 border-surface/25 lg:block dark:border-surface" />

          <div className="hidden lg:block">
            <NavList />
          </div>

          <div className="hidden lg:flex lg:ml-auto gap-2">
            <Button
              size="md"
              className="border-white bg-white text-black hover:border-white hover:bg-white hover:text-black"
              onClick={() => handleOpenModal("login")}
            >
              Login
            </Button>
            <Button
              size="md"
              className="border-white bg-white text-black hover:border-white hover:bg-white hover:text-black"
              onClick={() => handleOpenModal("register")}
            >
              Register
            </Button>
          </div>

          <IconButton
            size="sm"
            color="secondary"
            onClick={() => setOpenNav(!openNav)}
            className="ml-auto grid lg:hidden"
          >
            {openNav ? (
              <Xmark className="h-4 w-4" />
            ) : (
              <Menu className="h-4 w-4" />
            )}
          </IconButton>
        </div>

        <Collapse open={openNav}>
          <NavList />
          <Button
            size="sm"
            isFullWidth
            className="mt-4 border-white bg-white text-black hover:border-white hover:bg-white hover:text-black"
            onClick={() => handleOpenModal("login")}
          >
            Login
          </Button>
          <Button
            size="sm"
            isFullWidth
            className="mt-2 border-white bg-white text-black hover:border-white hover:bg-white hover:text-black"
            onClick={() => handleOpenModal("register")}
          >
            Register
          </Button>
        </Collapse>
      </Navbar>

      {type === "login" ? (
        <ModalLogin open={openModal} onClose={handleCloseModal} />
      ) : (
        <ModalRegister open={openModal} onClose={handleCloseModal} />
      )}
    </>
  );
}
