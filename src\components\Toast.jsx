import React, { useContext, useEffect, useState } from "react";
import { ToastContext } from "../contexts/ToastContext"; 

export default function Toast() {
  const { toast } = useContext(ToastContext);
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (toast) {
      setVisible(true);
      const timer = setTimeout(() => {
        setVisible(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [toast]);

  if (!toast || !visible) return null;

  // تعيين ألوان حسب النوع
  const colors = {
    success: "bg-green-100 text-green-800 border-green-500",
    error: "bg-red-100 text-red-800 border-red-500",
    warning: "bg-yellow-100 text-yellow-800 border-yellow-500",
    info: "bg-blue-100 text-blue-800 border-blue-500",
  };

  const colorClass = colors[toast.type] || colors.info;

  return (
    <div
      className={`fixed bottom-5 right-5 z-50 border-l-4 px-4 py-3 rounded shadow-lg max-w-sm ${colorClass} transition-opacity duration-300`}
      role="alert"
    >
      <p className="font-medium">{toast.message}</p>
    </div>
  );
}
